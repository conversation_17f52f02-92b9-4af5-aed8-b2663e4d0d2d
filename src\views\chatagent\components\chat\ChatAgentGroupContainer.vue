<!--
 * Copyright 2025 the original author or authors.
 *
 * Licensed under the Apache License, Version 2.0 (the "License");
 * you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 *
 *      https://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
-->
<template>
  <div class="agent-group-container" v-if="agentGroup.agentName !== 'SUMMARY_AGENT'" :data-agent-id="agentGroup.agentExecutionId" :data-agent-index="agentIndex">
    <!-- Agent头部 -->
    <div class="agent-header"  @click="toggleExpanded">
      <!-- 时间线节点 -->
      <div class="timeline-node" :ref="`timelineNode${agentIndex}`">
        <div class="timeline-icon" :class="getAgentStatusClass(agentGroup.agentStatus)">
          <Icon :icon="getAgentStatusIcon(agentGroup.agentStatus)" />
        </div>
        <div
          class="timeline-connector"
          v-if="!isLastAgent"
          :style="{ height: currentConnectorHeight + 'px' }"
        ></div>
      </div>

      <div class="agent-info">
        <Icon icon="carbon:bot" class="agent-icon" />
        <span class="agent-name">{{ agentGroup.agentDescription || agentGroup.agentName }}</span>
        <span class="agent-status" :class="getAgentStatusClass(agentGroup.agentStatus)">
          {{ getAgentStatusText(agentGroup.agentStatus) }}
        </span>
      </div>
      <div class="agent-toggle" >
        <Icon :icon="isExpanded ? 'carbon:chevron-up' : 'carbon:chevron-down'" />
      </div>
    </div>

    <!-- Agent步骤列表 -->
    <div v-if="isExpanded" class="agent-steps">
      <div
        v-for="mergedStep in mergedSteps"
        :key="mergedStep.id"
        :class="{ 'has-tool step-item': mergedStep.actionNeeded && mergedStep.toolName }"
      >
        <!-- 思考输出 - 简化显示，无边框 -->
         
        <div v-if="(mergedStep.thinkOutput&&mergedStep.actionNeeded) || (mergedStep.agentName == 'SUMMARY_AGENT')" class="think-content-simple">
          {{ mergedStep.thinkOutput }}
        </div>
        <!-- 工具调用 - 添加边框 -->
        <div v-if="mergedStep.actionNeeded && mergedStep.toolName"
             class="tool-section-boxed"
             :class="{ 'tool-highlighted': highlightedToolId === mergedStep.id }"
             @click="handleToolClick(mergedStep)">
          <div class="tool-header">
            <div class="tool-info">
              <Icon icon="carbon:tool-box" />
              <span>调用工具: {{ mergedStep.toolName }}</span>
            </div>
            <div class="tool-status-inline">
              <div class="status-indicator" :class="getToolStatusClass(mergedStep.status)">
                <!-- <Icon :icon="getToolStatusIcon(mergedStep.status)" /> -->
                <span>{{ getToolStatusText(mergedStep.status) }}</span>
              </div>
            </div>
          </div>

          <div v-if="mergedStep.thinkStartTime" class="tool-time-info">
            <span>开始: {{ formatTime(mergedStep.thinkStartTime) }}</span>
            <span v-if="mergedStep.actEndTime">
              | 结束: {{ formatTime(mergedStep.actEndTime) }}
            </span>
          </div>
        </div>

        <!-- 错误信息 -->
        <div v-if="mergedStep.errorInfo" class="error-section">
          <div class="section-header error">
            <Icon icon="carbon:warning" />
            <span>执行错误</span>
          </div>
          <div class="error-content">
            {{ mergedStep.errorInfo }}
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, reactive, computed, onMounted, onUnmounted, nextTick, watch } from 'vue'
import { Icon } from '@iconify/vue'
import dayjs from 'dayjs'

// 全局状态管理多个Agent的连接线高度 - 移到模块级别以在所有组件实例间共享
const globalConnectorHeights = reactive<Record<string, number>>({})

interface ExecutionStepData {
  id: string
  thinkOutput?: string
  actionNeeded: boolean
  toolName?: string
  status: string
  thinkStartTime?: string
  actEndTime?: string
  toolInfo?: {
    parameters?: any
    result?: any
  }
  errorInfo?: string
  timestamp: Date
  planSummary?: string
}

interface AgentGroupData {
  agentExecutionId: number
  agentName: string
  agentDescription?: string
  agentStatus: string
  agentCompleted: boolean
  steps: ExecutionStepData[]
}

const props = defineProps<{
  agentGroup: AgentGroupData
  agentIndex?: number
  totalAgents?: number
  isLastAgent?: boolean
  clearHighlightSignal?: number
}>()

const emit = defineEmits<{
  toolClick: [stepId: string, toolName: string, stepData: ExecutionStepData, agentExecutionId: number]
}>()

const isExpanded = ref(true)
const stepDetailsVisible = reactive<Record<string, boolean>>({})
const highlightedToolId = ref<string | null>(null)

// 当前组件实例的对话轮次ID
const currentRoundId = ref<string>('default')

// 生成唯一的连接线键，使用agentExecutionId确保全局唯一
const getConnectorKey = () => {
  return `${currentRoundId.value}-${props.agentGroup.agentExecutionId}`
}

// 获取当前Agent的连接线高度
const currentConnectorHeight = computed(() => {
  const key = getConnectorKey()
  const height = globalConnectorHeights[key] || 60 // 默认高度60px

  // 调试信息
  console.log(`🔗 [Agent ${props.agentGroup.agentExecutionId}] 连接线高度计算:`, {
    roundId: currentRoundId.value,
    agentExecutionId: props.agentGroup.agentExecutionId,
    key,
    height,
    allHeights: globalConnectorHeights
  })

  return height
})

// 初始化对话轮次ID
const initializeRoundId = () => {
  nextTick(() => {
    const container = document.querySelector(`[data-agent-id="${props.agentGroup.agentExecutionId}"]`)
    const parentRound = container?.closest('.conversation-round')
    if (parentRound) {
      const roundId = parentRound.getAttribute('data-round-id') || 'default'
      currentRoundId.value = roundId
      console.log(`🎯 [Agent ${props.agentGroup.agentExecutionId}] 初始化对话轮次ID:`, roundId)
    } else {
      console.warn(`⚠️ [Agent ${props.agentGroup.agentExecutionId}] 未找到父对话轮次容器`)
    }
  })
}

// ResizeObserver 和 MutationObserver 实例
let resizeObserver: ResizeObserver | null = null
let mutationObserver: MutationObserver | null = null
let windowResizeHandler: (() => void) | null = null

// 计算当前对话轮次内所有Agent的连接线高度
const calculateCurrentRoundConnectorHeights = async () => {
  await nextTick()

  try {
    // 获取当前组件所在的对话轮次容器
    const currentContainer = document.querySelector(`[data-agent-id="${props.agentGroup.agentExecutionId}"]`)
    const parentRound = currentContainer?.closest('.conversation-round')
    if (!parentRound) {
      console.warn('未找到父对话轮次容器')
      return
    }

    const roundId = parentRound.getAttribute('data-round-id') || 'default'

    // 只查找当前对话轮次内的Agent容器，按agentIndex排序
    const roundContainers = Array.from(parentRound.querySelectorAll('.agent-group-container[data-agent-id]'))
      .sort((a, b) => {
        const indexA = parseInt(a.getAttribute('data-agent-index') || '0')
        const indexB = parseInt(b.getAttribute('data-agent-index') || '0')
        return indexA - indexB
      })

    roundContainers.forEach((container, index) => {
      const agentId = container.getAttribute('data-agent-id')
      const agentIndex = parseInt(container.getAttribute('data-agent-index') || '0')

      // 查找下一个Agent容器
      const nextContainer = roundContainers[index + 1]

      if (nextContainer) {
        const currentRect = container.getBoundingClientRect()
        const nextRect = nextContainer.getBoundingClientRect()

        // 计算从当前Agent头部中心到下一个Agent头部中心的距离
        const currentHeaderHeight = 68 // Agent头部高度约68px
        const distance = nextRect.top - currentRect.top - currentHeaderHeight / 2

        // 设置最小高度60px，最大高度500px
        const newHeight = Math.max(60, Math.min(500, distance))

        // 使用agentId生成连接线键
        const key = `${roundId}-${agentId}`

        // 只有高度变化超过5px时才更新，避免频繁更新
        const currentHeight = globalConnectorHeights[key] || 60
        if (Math.abs(currentHeight - newHeight) > 5) {
          globalConnectorHeights[key] = newHeight
          console.log(`📏 [Agent ${agentId}] 更新连接线高度:`, {
            roundId,
            agentIndex,
            key,
            oldHeight: currentHeight,
            newHeight,
            distance
          })
        }
      }
    })
  } catch (error) {
    console.warn('计算连接线高度失败:', error)
  }
}

// 兼容旧接口 - 重定向到新的函数
const calculateAllConnectorHeights = calculateCurrentRoundConnectorHeights
console.log('props agentGroup', props.agentGroup)
// 计算当前Agent的连接线高度（兼容旧接口）
const calculateConnectorHeight = async () => {
  if (props.isLastAgent) return

  await nextTick()

  try {
    const currentContainer = document.querySelector(`[data-agent-id="${props.agentGroup.agentExecutionId}"]`)

    // 在同一个对话轮次中查找下一个Agent
    const parentRound = currentContainer?.closest('.conversation-round')
    if (!parentRound) return

    const agentIndex = props.agentIndex || 0
    const nextContainer = parentRound.querySelector(`[data-agent-index="${agentIndex + 1}"]`)

    if (currentContainer && nextContainer) {
      const currentRect = currentContainer.getBoundingClientRect()
      const nextRect = nextContainer.getBoundingClientRect()

      // 计算从当前Agent头部中心到下一个Agent头部中心的距离
      const currentHeaderHeight = 68 // Agent头部高度约68px
      const distance = nextRect.top - currentRect.top - currentHeaderHeight / 2

      // 设置最小高度60px，最大高度500px
      const newHeight = Math.max(60, Math.min(500, distance))

      // 生成连接线键
      const key = getConnectorKey()

      // 只有高度变化超过5px时才更新，避免频繁更新
      const currentHeight = globalConnectorHeights[key] || 60
      if (Math.abs(currentHeight - newHeight) > 5) {
        globalConnectorHeights[key] = newHeight
      }
    }
  } catch (error) {
    console.warn('计算连接线高度失败:', error)
    // 回退到默认值
    const key = getConnectorKey()
    globalConnectorHeights[key] = 60
  }
}

// 防抖函数
const debounce = (func: Function, wait: number) => {
  let timeout: NodeJS.Timeout
  return function executedFunction(...args: any[]) {
    const later = () => {
      clearTimeout(timeout)
      func(...args)
    }
    clearTimeout(timeout)
    timeout = setTimeout(later, wait)
  }
}

// 防抖的计算函数 - 计算所有Agent的高度
const debouncedCalculateAllHeights = debounce(calculateAllConnectorHeights, 100)
// 防抖的计算函数 - 计算当前Agent的高度
const debouncedCalculateHeight = debounce(calculateConnectorHeight, 100)

// 设置观察器 - 只观察当前对话轮次
const setupObservers = () => {
  // 获取当前对话轮次容器
  const currentContainer = document.querySelector(`[data-agent-id="${props.agentGroup.agentExecutionId}"]`)
  const parentRound = currentContainer?.closest('.conversation-round')
  if (!parentRound) {
    console.warn('未找到父对话轮次容器，无法设置观察器')
    return
  }

  // 1. 窗口大小变化监听 - 只影响当前轮次
  windowResizeHandler = () => {
    debouncedCalculateAllHeights()
  }
  window.addEventListener('resize', windowResizeHandler)

  // 2. 监听当前轮次内容器大小变化
  if (window.ResizeObserver) {
    resizeObserver = new ResizeObserver((entries) => {
      // 检查是否有当前轮次的Agent容器发生了大小变化
      const hasRelevantChange = entries.some(entry => {
        const element = entry.target as HTMLElement
        const elementRound = element.closest('.conversation-round')
        return elementRound === parentRound && element.hasAttribute('data-agent-id')
      })

      if (hasRelevantChange) {
        // 只重新计算当前轮次的高度
        debouncedCalculateAllHeights()
      }
    })

    // 只观察当前轮次的Agent容器
    const roundContainers = parentRound.querySelectorAll('.agent-group-container[data-agent-id]')
    roundContainers.forEach(container => {
      resizeObserver?.observe(container)
    })
  }

  // 3. 监听当前轮次DOM变化（新增Agent容器）
  if (window.MutationObserver) {
    mutationObserver = new MutationObserver((mutations) => {
      let shouldRecalculate = false

      mutations.forEach(mutation => {
        if (mutation.type === 'childList') {
          // 检查是否有新增的Agent容器在当前轮次
          mutation.addedNodes.forEach(node => {
            if (node.nodeType === Node.ELEMENT_NODE) {
              const element = node as HTMLElement
              const elementRound = element.closest?.('.conversation-round')

              if (elementRound === parentRound &&
                  (element.hasAttribute?.('data-agent-id') ||
                   element.querySelector?.('[data-agent-id]'))) {
                shouldRecalculate = true

                // 为新增的容器添加ResizeObserver
                if (resizeObserver && element.hasAttribute('data-agent-id')) {
                  resizeObserver.observe(element)
                }
              }
            }
          })
        }
      })

      if (shouldRecalculate) {
        // 延迟一点时间确保新元素完全渲染
        setTimeout(() => {
          debouncedCalculateAllHeights()
        }, 150)
      }
    })

    // 观察当前轮次容器的变化
    mutationObserver.observe(parentRound, {
      childList: true,
      subtree: true
    })
  }
}

// 清理观察器
const cleanupObservers = () => {
  if (windowResizeHandler) {
    window.removeEventListener('resize', windowResizeHandler)
    windowResizeHandler = null
  }

  if (resizeObserver) {
    resizeObserver.disconnect()
    resizeObserver = null
  }

  if (mutationObserver) {
    mutationObserver.disconnect()
    mutationObserver = null
  }
}

// 监听props变化，重新设置观察器
watch(() => [props.agentIndex, props.isLastAgent], () => {
  // 只有每个对话轮次的第一个Agent负责设置该轮次的观察器
  if (props.agentIndex === 0) {
    cleanupObservers()
    nextTick(() => {
      setupObservers()
      calculateCurrentRoundConnectorHeights()
    })
  }
}, { immediate: false })

// 监听展开/折叠状态变化
watch(() => isExpanded.value, () => {
  // 展开/折叠后重新计算当前轮次的高度
  nextTick(() => {
    debouncedCalculateAllHeights()
  })
})

onMounted(() => {
  // 初始化对话轮次ID
  initializeRoundId()

  // 只有每个对话轮次的第一个Agent负责初始化该轮次的观察器和计算高度
  if (props.agentIndex === 0) {
    calculateCurrentRoundConnectorHeights()
    setupObservers()
  } else {
    // 其他Agent只计算自己的高度
    calculateConnectorHeight()
  }
})

onUnmounted(() => {
  // 只有每个对话轮次的第一个Agent负责清理该轮次的观察器
  if (props.agentIndex === 0) {
    cleanupObservers()
  }
})

// 合并相同工具调用的步骤
const mergedSteps = computed(() => {
  const merged = new Map()
  const thinkOnlySteps: ExecutionStepData[] = []

  props.agentGroup.steps.forEach(step => {
    if (step.actionNeeded && step.toolName) {
      // 有工具调用的步骤
      const toolKey = step.toolName

      if (!merged.has(toolKey)) {
        // 第一次遇到这个工具调用，创建合并步骤
        merged.set(toolKey, {
          id: step.id,
          thinkOutput: step.thinkOutput,
          actionNeeded: step.actionNeeded,
          toolName: step.toolName,
          status: step.status,
          thinkStartTime: step.thinkStartTime,
          actEndTime: step.actEndTime,
          toolInfo: step.toolInfo,
          errorInfo: step.errorInfo,
          timestamp: step.timestamp,
          planSummary: step.planSummary
        })
      } else {
        // 更新现有的工具调用步骤（通常是状态更新）
        const existing = merged.get(toolKey)

        // 更新状态和结束时间（保持最新状态）
        existing.status = step.status
        if (step.actEndTime) {
          existing.actEndTime = step.actEndTime
        }
        if (step.toolInfo) {
          existing.toolInfo = step.toolInfo
        }
        if (step.errorInfo) {
          existing.errorInfo = step.errorInfo
        }

        // 如果新步骤有思考输出且现有步骤没有，则添加
        if (step.thinkOutput && !existing.thinkOutput) {
          existing.thinkOutput = step.thinkOutput
        }
        if (step.planSummary && !existing.planSummary) {
          existing.planSummary = step.planSummary
        }
      }
    } else if (step.thinkOutput) {
      // 只有思考输出，没有工具调用的步骤
      thinkOnlySteps.push(step)
    }
  })

  // 合并结果：先是纯思考步骤，然后是工具调用步骤
  return [...thinkOnlySteps, ...Array.from(merged.values())]
})


const toggleExpanded = async () => {
  isExpanded.value = !isExpanded.value
  console.log(`🔄 [Agent ${props.agentGroup.agentExecutionId}] 切换展开状态:`, {
    roundId: currentRoundId.value,
    expanded: isExpanded.value,
    agentIndex: props.agentIndex,
    agentExecutionId: props.agentGroup.agentExecutionId
  })

  // 展开/折叠后重新计算当前轮次的连接线高度
  await nextTick()
  debouncedCalculateAllHeights()
}

const toggleStepDetails = (stepId: string) => {
  stepDetailsVisible[stepId] = !stepDetailsVisible[stepId]
}

const handleToolClick = (stepData: ExecutionStepData) => {
  console.log('🔧 [ChatAgentGroupContainer] 点击工具:', {
    toolName: stepData.toolName,
    stepId: stepData.id,
    stepData: stepData
  })

  // 设置高亮状态
  highlightedToolId.value = stepData.id

  // 添加调试信息
  console.log('🔧 [ChatAgentGroupContainer] 发送toolClick事件到父组件')

  emit('toolClick', stepData.id, stepData.toolName || '', stepData, props.agentGroup.agentExecutionId)
}

// 清除工具高亮状态的方法
const clearToolHighlight = () => {
  console.log('🔧 [ChatAgentGroupContainer] clearToolHighlight 被调用')
  
  highlightedToolId.value = null
  console.log('🔧 [ChatAgentGroupContainer] 高亮状态已清除')
}

// 监听清除高亮信号
watch(() => props.clearHighlightSignal, (newVal) => {
  if (newVal && newVal > 0) {
    console.log('🔧 [ChatAgentGroupContainer] 收到清除高亮信号:', newVal)
    clearToolHighlight()
  }
})

const getAgentStatusClass = (status: string) => {
  const statusMap = {
    'in_progress': 'status-running',
    'completed': 'status-completed',
    'COMPLETED': 'status-completed',
    'failed': 'status-failed',
    'pending': 'status-pending'
  }
  return statusMap[status] || 'status-unknown'
}

const getAgentStatusText = (status: string) => {
  const statusMap = {
    'in_progress': '执行中',
    'completed': '已完成',
    'COMPLETED': '已完成',
    'failed': '执行失败',
    'pending': '等待中'
  }
  return statusMap[status] || status
}

const getAgentStatusIcon = (status: string) => {
  const iconMap = {
    'in_progress': 'carbon:circle-dash',
    'completed': 'carbon:checkmark-filled',
    'COMPLETED': 'carbon:checkmark-filled',
    'failed': 'carbon:error-filled',
    'pending': 'carbon:time'
  }
  return iconMap[status] || 'carbon:help'
}

const getToolStatusClass = (status: string) => {
  const statusMap = {
    'RUNNING': 'status-running',
    'FINISHED': 'status-completed',
    'FISISHED': 'status-completed',
    'FAILED': 'status-failed',
    'PENDING': 'status-pending'
  }
  return statusMap[status] || 'status-unknown'
}

const getToolStatusIcon = (status: string) => {
  const iconMap = {
    'RUNNING': 'carbon:circle-dash',
    'FINISHED': 'carbon:checkmark-filled',
    'FAILED': 'carbon:error-filled',
    'PENDING': 'carbon:time'
  }
  return iconMap[status] || 'carbon:help'
}

const getToolStatusText = (status: string) => {
  const statusMap = {
    'RUNNING': '执行中',
    'FINISHED': '已完成',
    'FISISHED': '已完成',
    'FAILED': '执行失败',
    'PENDING': '等待中'
  }
  return statusMap[status] || status
}

const formatTime = (timeStr: string) => {
  return dayjs(timeStr).format('HH:mm:ss')
}

const formatJson = (obj: any) => {
  if (!obj) return ''
  try {
    return JSON.stringify(obj, null, 2)
  } catch (e) {
    return String(obj)
  }
}

// 暴露方法给父组件
defineExpose({
  clearToolHighlight
})
</script>

<style lang="less" scoped>
.agent-group-container {
  margin: 16px 0;
  // border: 1px solid #e5e7eb;
  border-radius: 12px;
  background: white;
  // overflow: hidden;
  // box-shadow: 0 2px 4px rgba(0, 0, 0, 0.05);
  animation: agentSlideIn 0.3s ease;
}

@keyframes agentSlideIn {
  from {
    opacity: 0;
    transform: translateY(10px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

.agent-header {
  background: #e9e9e9;
  border-bottom: 1px solid #e2e8f0;
  color: #1e293b;
  padding: 10px 0px;
  display: flex;
  align-items: center;
  justify-content: space-between;
  cursor: pointer;
  transition: all 0.2s ease;
  position: relative;

  &:hover {
    background: #f1f5f9;
  }
}

/* 时间线节点样式 */
.timeline-node {
  position: absolute;
  left: -40px;
  top: 50%;
  transform: translateY(-50%);
  display: flex;
  flex-direction: column;
  align-items: center;
  z-index: 10;
}

.timeline-icon {
  width: 32px;
  height: 32px;
  border-radius: 50%;
  color: white;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 16px;
  border: 3px solid white;
  position: relative;
  z-index: 11;

  &.status-pending {
    background: #9ca3af;
    box-shadow: 0 2px 8px rgba(156, 163, 175, 0.3);
    animation: spin 1s linear infinite;
  }

  &.status-running {
    background: #f59e0b;
    box-shadow: 0 2px 8px rgba(245, 158, 11, 0.3);
    animation: spin 1s linear infinite;
  }

  &.status-completed {
    background: #10b981;
    box-shadow: 0 2px 8px rgba(16, 185, 129, 0.3);
  }

  &.status-failed {
    background: #ef4444;
    box-shadow: 0 2px 8px rgba(239, 68, 68, 0.3);
  }

  &.status-unknown {
    background: #6b7280;
    box-shadow: 0 2px 8px rgba(107, 114, 128, 0.3);
  }
}
@keyframes spin {
  from { transform: rotate(0deg); }
  to { transform: rotate(360deg); }
}
.timeline-connector {
  position: absolute;
  top: 32px;
  left: 50%;
  transform: translateX(-50%);
  width: 2px;
  background: #d1d5db;
  border-left: 2px dashed #d1d5db;
  background: transparent;
  z-index: 9;
  transition: height 0.3s ease; /* 添加高度变化的过渡动画 */
}

.agent-info {
  display: flex;
  align-items: center;
  gap: 12px;
  flex: 1;
  margin-left: 20px; /* 为时间线节点留出空间 */
}

.agent-icon {
  font-size: 1.2rem;
}

.agent-name {
  font-weight: 600;
  font-size: 1rem;
  flex: 1;
}

.agent-status {
  font-size: 0.75rem;
  padding: 4px 8px;
  border-radius: 12px;
  background: rgba(255, 255, 255, 0.2);
  font-weight: 500;

  &.status-running {
    background: rgba(251, 191, 36, 0.8);
  }

  &.status-completed {
    background: rgba(16, 185, 129, 0.8);
  }

  &.status-failed {
    background: rgba(239, 68, 68, 0.8);
  }

  &.status-pending {
    background: rgba(107, 114, 128, 0.8);
  }
}

.agent-toggle {
  padding: 4px;
  border-radius: 4px;
  transition: background 0.2s ease;

  &:hover {
    background: rgba(255, 255, 255, 0.1);
  }
}

.agent-steps {
  padding: 0;
}

.step-item {
  padding: 16px 20px;
  border-bottom: 1px solid #f3f4f6;

  &:last-child {
    border-bottom: none;
  }
}

// 简化的思考内容样式
.think-content-simple {
  color: #4b5563;
  font-size: 0.875rem;
  line-height: 1.6;
  margin-bottom: 12px;
  padding: 0;
}

// 工具调用边框样式
.tool-section-boxed {
  border: 1px solid #d1d5db;
  border-radius: 8px;
  background: #f9fafb;
  margin: 8px 0;
  overflow: hidden;
  transition: all 0.3s ease;

  &.tool-highlighted {
    border-color: #3b82f6;
    background: #eff6ff;
    box-shadow: 0 0 0 2px rgba(59, 130, 246, 0.1);
  }
  cursor: pointer;
  transition: all 0.2s ease;

  &:hover {
    border-color: #9ca3af;
    box-shadow: 0 2px 4px rgba(0, 0, 0, 0.05);
  }
}

.tool-header {
  padding: 12px 16px;
  display: flex;
  align-items: center;
  justify-content: space-between;
  background: white;
  border-bottom: 1px solid #e5e7eb;
}

.tool-info {
  display: flex;
  align-items: center;
  gap: 8px;
  font-weight: 500;
  color: #374151;
  font-size: 0.875rem;
}

.tool-status-inline {
  display: flex;
  align-items: center;
}

.tool-time-info {
  padding: 8px 16px;
  font-size: 0.75rem;
  color: #6b7280;
  background: #f9fafb;

  span {
    margin-right: 8px;
  }
}

.section-header {
  display: flex;
  align-items: center;
  gap: 8px;
  margin-bottom: 8px;
  font-weight: 500;
  color: #1f2937;
  font-size: 0.875rem;

  &.error {
    color: #dc2626;
  }
}

.detail-btn {
  margin-left: auto;
  background: #f3f4f6;
  border: 1px solid #d1d5db;
  border-radius: 6px;
  padding: 4px 8px;
  cursor: pointer;
  display: flex;
  align-items: center;
  gap: 4px;
  font-size: 0.75rem;
  color: #6b7280;
  transition: all 0.2s ease;

  &:hover {
    background: #e5e7eb;
    color: #374151;
  }
}

.think-section {
  margin-bottom: 12px;
}

.think-content {
  background: #f9fafb;
  border: 1px solid #e5e7eb;
  border-radius: 8px;
  padding: 12px;
  font-size: 0.875rem;
  line-height: 1.6;
  white-space: pre-wrap;
  color: #374151;
}

.tool-section {
  margin-bottom: 12px;
}

.tool-details {
  margin: 12px 0;
  border: 1px solid #e5e7eb;
  border-radius: 8px;
  background: #f9fafb;
}

.detail-section {
  padding: 12px;
  border-bottom: 1px solid #e5e7eb;

  &:last-child {
    border-bottom: none;
  }

  h4 {
    margin: 0 0 8px 0;
    font-size: 0.875rem;
    font-weight: 500;
    color: #374151;
  }
}

.json-content {
  background: #1f2937;
  color: #f9fafb;
  padding: 8px;
  border-radius: 4px;
  font-size: 0.75rem;
  overflow-x: auto;
  margin: 0;
  white-space: pre-wrap;
}

.tool-status {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 8px 12px;
  background: #f9fafb;
  border-radius: 6px;
  font-size: 0.75rem;
}

.status-indicator {
  display: flex;
  align-items: center;
  gap: 4px;
  font-weight: 500;

  &.status-running {
    color: #f59e0b;
  }

  &.status-completed {
    color: #10b981;
  }

  &.status-failed {
    color: #ef4444;
  }

  &.status-pending {
    color: #6b7280;
  }
}

.time-info {
  color: #6b7280;

  span {
    margin-right: 8px;
  }
}

.error-section {
  margin-bottom: 12px;
}

.error-content {
  background: #fef2f2;
  border: 1px solid #fecaca;
  border-radius: 6px;
  padding: 12px;
  font-size: 0.875rem;
  color: #dc2626;
}
</style>
